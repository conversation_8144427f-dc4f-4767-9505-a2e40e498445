const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("FlashloanArbitrage", function () {
  let flashloanArbitrage;
  let owner;
  let addr1;
  
  // Aave V3 Pool Address Provider on mainnet fork
  const AAVE_ADDRESS_PROVIDER = "******************************************";
  
  beforeEach(async function () {
    [owner, addr1] = await ethers.getSigners();
    
    const FlashloanArbitrage = await ethers.getContractFactory("FlashloanArbitrage");
    flashloanArbitrage = await FlashloanArbitrage.deploy(AAVE_ADDRESS_PROVIDER);
    await flashloanArbitrage.waitForDeployment();
  });
  
  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await flashloanArbitrage.owner()).to.equal(owner.address);
    });
    
    it("Should initialize with correct Aave address provider", async function () {
      // This test verifies the contract was deployed with correct parameters
      expect(await flashloanArbitrage.getAddress()).to.be.properAddress;
    });
  });
  
  describe("DEX Management", function () {
    it("Should allow owner to add DEX", async function () {
      const testRouter = "******************************************";
      
      await expect(flashloanArbitrage.addDEX(testRouter, 0))
        .to.emit(flashloanArbitrage, "DEXAdded")
        .withArgs(testRouter, 0);
    });
    
    it("Should not allow non-owner to add DEX", async function () {
      const testRouter = "******************************************";
      
      await expect(
        flashloanArbitrage.connect(addr1).addDEX(testRouter, 0)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });
    
    it("Should allow owner to remove DEX", async function () {
      const testRouter = "******************************************";
      
      // First add a DEX
      await flashloanArbitrage.addDEX(testRouter, 0);
      
      // Then remove it
      await expect(flashloanArbitrage.removeDEX(testRouter))
        .to.emit(flashloanArbitrage, "DEXRemoved")
        .withArgs(testRouter);
    });
  });
  
  describe("Active DEXs", function () {
    it("Should return list of active DEXs", async function () {
      const activeDEXs = await flashloanArbitrage.getActiveDEXs();
      
      // Should have the default DEXs added in constructor
      expect(activeDEXs.length).to.be.greaterThan(0);
    });
  });
  
  describe("Gas Estimation", function () {
    it("Should estimate gas for arbitrage operation", async function () {
      const arbParams = {
        tokenA: "******************************************", // WETH
        tokenB: "******************************************", // USDC
        amount: ethers.parseEther("1"),
        dexRouters: ["******************************************"],
        swapData: ["0x"],
        minProfit: ethers.parseEther("0.01")
      };
      
      const gasEstimate = await flashloanArbitrage.estimateGas(
        arbParams.tokenA,
        arbParams.amount,
        arbParams
      );
      
      expect(gasEstimate).to.be.greaterThan(0);
    });
  });
  
  describe("Owner Functions", function () {
    it("Should allow owner to withdraw ETH", async function () {
      // Send some ETH to the contract
      await owner.sendTransaction({
        to: await flashloanArbitrage.getAddress(),
        value: ethers.parseEther("1")
      });
      
      const initialBalance = await ethers.provider.getBalance(owner.address);
      
      await flashloanArbitrage.withdrawETH();
      
      const finalBalance = await ethers.provider.getBalance(owner.address);
      expect(finalBalance).to.be.greaterThan(initialBalance);
    });
    
    it("Should not allow non-owner to withdraw ETH", async function () {
      await expect(
        flashloanArbitrage.connect(addr1).withdrawETH()
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });
  });
});
