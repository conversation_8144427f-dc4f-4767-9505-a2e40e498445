{"name": "flashloan-arbitrage-bot", "version": "1.0.0", "description": "Ethereum flashloan arbitrage bot using Aave v3 and multiple DEX integrations", "main": "src/monitor.js", "scripts": {"compile": "hardhat compile", "deploy": "hardhat run scripts/deploy.js --network mainnet", "deploy:testnet": "hardhat run scripts/deploy.js --network goerli", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "test": "hardhat test", "monitor": "node src/monitor.js", "start": "npm run monitor", "verify": "hardhat run scripts/verify.js --network mainnet", "verify:testnet": "hardhat run scripts/verify.js --network goerli", "check": "hardhat run scripts/check-deployment.js --network mainnet", "check:testnet": "hardhat run scripts/check-deployment.js --network goerli", "fund": "hardhat run scripts/fund-contract.js --network mainnet", "fund:testnet": "hardhat run scripts/fund-contract.js --network goerli"}, "keywords": ["ethereum", "flashloan", "arbitrage", "defi", "aave", "uniswap", "dex"], "author": "Flashloan Arbitrage Bot", "license": "MIT", "dependencies": {"@aave/core-v3": "^1.19.3", "@aave/periphery-v3": "^2.0.3", "@openzeppelin/contracts": "^4.9.3", "@uniswap/v3-core": "^1.0.1", "@uniswap/v3-periphery": "^1.4.3", "@uniswap/v2-core": "^1.0.1", "@uniswap/v2-periphery": "^1.1.0-beta.0", "ethers": "^6.8.0", "dotenv": "^16.3.1", "axios": "^1.5.0", "ws": "^8.14.2", "node-cron": "^3.0.2", "winston": "^3.10.0", "bignumber.js": "^9.1.2"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.2", "@nomicfoundation/hardhat-verify": "^1.1.1", "hardhat": "^2.17.2", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.4", "@typechain/hardhat": "^8.0.3", "@typechain/ethers-v6": "^0.4.3", "typechain": "^8.3.1"}, "engines": {"node": ">=16.0.0"}}